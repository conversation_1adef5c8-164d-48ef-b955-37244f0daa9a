<script setup lang="ts">
import Markdown from "@/components/Markdown.vue";
import StandardToolCall from "@/components/StandardToolCall.vue";
import type { TextMessage, ToolCallMessage } from "~/types/message";
import {
  isText,
  isNonInteractiveToolCall,
  isArtifactReportToolCall,
  isRetrieveProcessTool,
} from "@/hooks/agent/message";
import { extractAndRemoveSummary } from "@/modules/sentire/helpers/message";
import ArtifactReportToolCall from "../ArtifactReportToolCall.vue";
import HiddenToolCall from "@/components/HiddenToolCall.vue";

defineProps<{
  messages: Array<TextMessage | ToolCallMessage>;
}>();
</script>

<template>
  <div>
    <div v-for="(content, messageIndex) in messages" :key="messageIndex">
      <div v-if="isText(content)">
        <Markdown
          :source="extractAndRemoveSummary((content as TextMessage).text).textWithoutSummary"
          class="prose prose-stone min-w-full prose-img:my-6 prose-img:mx-auto prose-sm prose-p:my-4 prose-p:text-base prose-p:leading-relaxed prose-headings:!font-semibold prose-headings:text-xl prose-headings:leading-snug prose-headings:mb-4 prose-li:text-base prose-ul:mb-4"
        />
      </div>
      <ArtifactReportToolCall
        v-else-if="isArtifactReportToolCall(content)"
        :report-id="(content as ToolCallMessage).result?.id as string"
        :status="(content as ToolCallMessage).result?.status as string"
        :title="(content as ToolCallMessage).result?.title as string"
        :message="(content as ToolCallMessage).result?.message as string"
        class="my-2"
      />
      <HiddenToolCall v-else-if="isRetrieveProcessTool(content)" />
      <StandardToolCall
        v-else-if="isNonInteractiveToolCall(content)"
        :name="(content as ToolCallMessage).toolName"
        :input="(content as ToolCallMessage).args"
        :output="(content as ToolCallMessage).result as Record<string, unknown>"
        :is-history-analysis="true"
        class="my-2"
      />
    </div>
  </div>
</template>
